# 媒资分片上传API文档

## 概述

媒资分片上传功能允许用户将大文件分成多个小块进行上传，提高上传效率和稳定性。该功能特别适用于大型视频文件的上传。

## API接口

### 1. 初始化分片上传

**接口地址：** `POST /video/media/initUpload`

**请求参数：**
- `fileName` (String, 必填): 文件名
- `fileSize` (Long, 必填): 文件大小（字节）
- `category` (String, 可选): 媒资分类，默认为"general"

**响应示例：**
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "uploadId": "upload-id-123456",
    "filePath": "ice/MediaLibrary/video/2025/07/26/1721984400000_example.mp4",
    "fileName": "example.mp4",
    "category": "video"
  }
}
```

### 2. 上传文件分片

**接口地址：** `POST /video/media/uploadChunk`

**请求参数：**
- `uploadId` (String, 必填): 上传ID（从初始化接口获取）
- `filePath` (String, 必填): 文件路径（从初始化接口获取）
- `chunkIndex` (Integer, 必填): 分片索引（从0开始）
- `chunk` (MultipartFile, 必填): 分片文件数据

**响应示例：**
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "etag": "d41d8cd98f00b204e9800998ecf8427e",
    "partNumber": 1,
    "md5": "5d41402abc4b2a76b9719d911017c592"  // 仅第一个分片返回
  }
}
```

### 3. 完成分片上传并注册媒资

**接口地址：** `POST /video/media/completeUpload`

**请求参数：**
- `uploadId` (String, 必填): 上传ID
- `filePath` (String, 必填): 文件路径
- `fileSize` (Long, 必填): 文件总大小
- `fileName` (String, 必填): 文件名
- `category` (String, 可选): 媒资分类，默认为"general"
- `partETags` (List<SysFilePartETag>, 必填): 分片ETag信息列表

**partETags 格式：**
```json
[
  {
    "partNumber": 1,
    "eTag": "d41d8cd98f00b204e9800998ecf8427e",
    "md5": "5d41402abc4b2a76b9719d911017c592"
  },
  {
    "partNumber": 2,
    "eTag": "098f6bcd4621d373cade4e832627b4f6"
  }
]
```

**响应示例：**
```json
{
  "code": 200,
  "msg": "媒资分片上传并注册成功",
  "data": {
    "mediaInfo": {
      "MediaId": "media-id-123456",
      "Status": "Normal"
    },
    "ossUrl": "oss://bucket-name/ice/MediaLibrary/video/2025/07/26/1721984400000_example.mp4",
    "filePath": "ice/MediaLibrary/video/2025/07/26/1721984400000_example.mp4",
    "uploadedPath": "https://bucket-name.oss-cn-hangzhou.aliyuncs.com/ice/MediaLibrary/video/2025/07/26/1721984400000_example.mp4",
    "fileName": "example.mp4",
    "fileSize": 10485760,
    "category": "video",
    "md5": "5d41402abc4b2a76b9719d911017c592"
  }
}
```

## 使用流程

### 1. 前端JavaScript示例

```javascript
class MediaChunkUploader {
    constructor(baseUrl) {
        this.baseUrl = baseUrl;
        this.chunkSize = 5 * 1024 * 1024; // 5MB per chunk
    }

    async uploadFile(file, category = 'general') {
        try {
            // 1. 初始化分片上传
            const initResponse = await this.initUpload(file.name, file.size, category);
            const { uploadId, filePath } = initResponse.data;

            // 2. 分片上传
            const partETags = await this.uploadChunks(file, uploadId, filePath);

            // 3. 完成上传并注册
            const completeResponse = await this.completeUpload(
                uploadId, filePath, file.size, file.name, category, partETags
            );

            return completeResponse.data;
        } catch (error) {
            console.error('Upload failed:', error);
            throw error;
        }
    }

    async initUpload(fileName, fileSize, category) {
        const formData = new FormData();
        formData.append('fileName', fileName);
        formData.append('fileSize', fileSize);
        formData.append('category', category);

        const response = await fetch(`${this.baseUrl}/video/media/initUpload`, {
            method: 'POST',
            body: formData
        });

        return await response.json();
    }

    async uploadChunks(file, uploadId, filePath) {
        const chunks = Math.ceil(file.size / this.chunkSize);
        const partETags = [];

        for (let i = 0; i < chunks; i++) {
            const start = i * this.chunkSize;
            const end = Math.min(start + this.chunkSize, file.size);
            const chunk = file.slice(start, end);

            const formData = new FormData();
            formData.append('uploadId', uploadId);
            formData.append('filePath', filePath);
            formData.append('chunkIndex', i);
            formData.append('chunk', chunk);

            const response = await fetch(`${this.baseUrl}/video/media/uploadChunk`, {
                method: 'POST',
                body: formData
            });

            const result = await response.json();
            if (result.code === 200) {
                partETags.push({
                    partNumber: result.data.partNumber,
                    eTag: result.data.etag,
                    md5: result.data.md5
                });
            } else {
                throw new Error(`Chunk ${i} upload failed: ${result.msg}`);
            }
        }

        return partETags;
    }

    async completeUpload(uploadId, filePath, fileSize, fileName, category, partETags) {
        const params = new URLSearchParams();
        params.append('uploadId', uploadId);
        params.append('filePath', filePath);
        params.append('fileSize', fileSize);
        params.append('fileName', fileName);
        params.append('category', category);

        const response = await fetch(`${this.baseUrl}/video/media/completeUpload?${params}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(partETags)
        });

        return await response.json();
    }
}

// 使用示例
const uploader = new MediaChunkUploader('http://localhost:8080');
const fileInput = document.getElementById('fileInput');

fileInput.addEventListener('change', async (event) => {
    const file = event.target.files[0];
    if (file) {
        try {
            const result = await uploader.uploadFile(file, 'video');
            console.log('Upload successful:', result);
        } catch (error) {
            console.error('Upload failed:', error);
        }
    }
});
```

## 注意事项

1. **文件大小限制：** 单个文件最大支持500MB
2. **分片大小建议：** 建议每个分片大小为5MB，可根据网络情况调整
3. **并发上传：** 建议控制并发分片数量，避免过多并发请求
4. **错误处理：** 上传失败时应该重试失败的分片，而不是重新开始整个上传过程
5. **进度显示：** 可以根据已上传分片数量计算上传进度
6. **文件类型：** 系统会根据文件扩展名自动识别媒资类型（video、audio、image、text）

## 错误码说明

- `200`: 操作成功
- `500`: 服务器内部错误
- 具体错误信息会在`msg`字段中返回

## 性能优化建议

1. **分片大小优化：** 根据网络带宽调整分片大小
2. **并发控制：** 限制同时上传的分片数量
3. **断点续传：** 记录已上传的分片，支持断点续传
4. **进度反馈：** 实时显示上传进度给用户
5. **错误重试：** 对失败的分片进行重试机制
